# Baby Tracker - Comprehensive Baby Care Application

A modern, responsive baby tracking application built with Next.js, TypeScript, and ShadCN UI components. Inspired by NARA BABY, this application provides an intuitive interface for tracking your baby's daily activities, growth, and development.

## 🌟 Features

### Core Tracking Features
- **Feeding Tracking**: Breastfeeding timers, bottle amounts, solid foods
- **Sleep Monitoring**: Nap and nighttime sleep tracking with timers
- **Diaper Changes**: Quick logging of wet/dirty diapers with notes
- **Growth Tracking**: Weight, height, head circumference with percentile charts
- **Quick Add Modal**: Fast entry for common activities
- **Today's Timeline**: Comprehensive view of daily activities

### User Experience
- **Responsive Design**: Optimized for mobile, tablet, and desktop
- **Clean Interface**: Designed for tired parents with quick-entry options
- **Visual Analytics**: Charts and progress indicators for tracking trends
- **Intuitive Navigation**: ShadCN dashboard layout with sidebar navigation
- **Real-time Timers**: Built-in timers for feeding and sleep tracking

### Technical Features
- **Modern Stack**: Next.js 14, TypeScript, Tailwind CSS
- **Component Library**: ShadCN UI for consistent, accessible components
- **Responsive Grid**: Mobile-first design with adaptive layouts
- **Performance Optimized**: Fast loading and smooth interactions
- **Type Safety**: Full TypeScript implementation

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd baby-tracker
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📱 Responsive Design

The application is fully responsive and optimized for:

- **Mobile (320px+)**: Touch-friendly interface with large buttons
- **Tablet (768px+)**: Optimized grid layouts and navigation
- **Desktop (1024px+)**: Full sidebar navigation and expanded views

### Key Responsive Features
- Collapsible sidebar navigation on mobile
- Adaptive grid layouts that stack on smaller screens
- Touch-optimized buttons and inputs
- Mobile-first CSS approach
- Responsive typography and spacing

## 🏗️ Architecture

### Project Structure
```
src/
├── app/                    # Next.js app directory
│   ├── page.tsx           # Dashboard page
│   ├── today/             # Today's activities
│   ├── feeding/           # Feeding tracking
│   ├── sleep/             # Sleep tracking
│   ├── diapers/           # Diaper tracking
│   └── growth/            # Growth monitoring
├── components/
│   ├── ui/                # ShadCN UI components
│   ├── dashboard/         # Layout components
│   └── activities/        # Activity tracking components
└── lib/
    └── utils.ts           # Utility functions
```

### Key Components

#### Dashboard Layout
- **Sidebar**: Responsive navigation with mobile menu
- **Header**: Page titles with search and notifications
- **Layout**: Consistent page structure across all routes

#### Activity Components
- **ActivityCard**: Reusable cards for different tracking types
- **QuickAddModal**: Modal for fast activity entry
- **Timer Components**: Real-time tracking for feeding and sleep

## 🎨 Design System

Built with ShadCN UI components for:
- **Consistency**: Unified design language
- **Accessibility**: WCAG compliant components
- **Customization**: Easy theming and styling
- **Performance**: Optimized component library

### Color Scheme
- **Primary**: Blue tones for main actions
- **Activity Colors**: 
  - Pink for feeding
  - Blue for sleep
  - Green for diapers
  - Purple for growth

## 🔧 Customization

### Adding New Tracking Categories
1. Create a new page in `src/app/[category]/page.tsx`
2. Add navigation item to `src/components/dashboard/sidebar.tsx`
3. Create activity card component
4. Add to quick-add modal

### Styling
- Modify `tailwind.config.js` for theme customization
- Update CSS variables in `src/app/globals.css`
- Customize ShadCN components in `src/components/ui/`

## 🚀 Future Enhancements

### Planned Features
- **Database Integration**: Supabase for data persistence
- **User Authentication**: Secure login and family sharing
- **Photo Attachments**: Milestone photos and memories
- **Export Functionality**: PDF reports for healthcare providers
- **Offline Support**: PWA with offline data sync
- **Push Notifications**: Feeding and medication reminders
- **Growth Charts**: WHO percentile charts integration
- **Analytics**: Advanced pattern recognition and insights

### Technical Improvements
- **Testing**: Unit and integration tests
- **Performance**: Image optimization and caching
- **SEO**: Meta tags and structured data
- **Accessibility**: Enhanced screen reader support

## 📊 Data Structure

### Activity Types
```typescript
interface Activity {
  id: string
  type: 'feeding' | 'sleep' | 'diaper' | 'growth'
  timestamp: Date
  duration?: number
  notes?: string
  // Type-specific fields
}
```

### Growth Tracking
```typescript
interface GrowthMeasurement {
  id: string
  date: Date
  weight?: number
  height?: number
  headCircumference?: number
  percentiles?: {
    weight: number
    height: number
    head: number
  }
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Inspired by NARA BABY's user-centered design
- Built with ShadCN UI components
- Designed for the needs of new parents

---

**Note**: This is a demonstration application. For production use, implement proper authentication, data persistence, and security measures.

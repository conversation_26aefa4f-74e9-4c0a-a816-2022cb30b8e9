"use client"

import { DashboardLayout } from "@/components/dashboard/layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Users, 
  Plus,
  UserPlus,
  Crown,
  Shield,
  Eye
} from "lucide-react"

export default function FamilyPage() {
  // Mock family data
  const familyMembers = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Parent",
      permissions: "Full Access",
      avatar: "JD",
      isOwner: true
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Parent",
      permissions: "Full Access",
      avatar: "J<PERSON>",
      isOwner: false
    },
    {
      id: 3,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Caregiver",
      permissions: "View Only",
      avatar: "GS",
      isOwner: false
    }
  ]

  const babies = [
    {
      id: 1,
      name: "<PERSON>",
      birthDate: "2024-01-15",
      age: "3 months",
      avatar: "EM"
    }
  ]

  return (
    <DashboardLayout
      title="Family"
      description="Manage family members and caregivers"
      headerAction={
        <Button>
          <UserPlus className="h-4 w-4 mr-2" />
          Invite Member
        </Button>
      }
    >
      <div className="space-y-6">
        {/* Babies */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Babies
              </CardTitle>
              <Button variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Baby
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {babies.map((baby) => (
                <div
                  key={baby.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-pink-600">
                        {baby.avatar}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium">{baby.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Born {baby.birthDate} • {baby.age} old
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      Edit
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Family Members */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Family Members & Caregivers
              </CardTitle>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                Invite Member
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {familyMembers.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-600">
                        {member.avatar}
                      </span>
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{member.name}</p>
                        {member.isOwner && (
                          <Crown className="h-4 w-4 text-yellow-500" />
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {member.email} • {member.role}
                      </p>
                      <div className="flex items-center space-x-1 mt-1">
                        {member.permissions === "Full Access" ? (
                          <Shield className="h-3 w-3 text-green-500" />
                        ) : (
                          <Eye className="h-3 w-3 text-blue-500" />
                        )}
                        <span className="text-xs text-muted-foreground">
                          {member.permissions}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {!member.isOwner && (
                      <>
                        <Button variant="ghost" size="sm">
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                          Remove
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Permissions Guide */}
        <Card>
          <CardHeader>
            <CardTitle>Permission Levels</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium">Full Access</p>
                  <p className="text-sm text-muted-foreground">
                    Can add, edit, and delete all activities. Can manage family members and settings.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Eye className="h-5 w-5 text-blue-500 mt-0.5" />
                <div>
                  <p className="font-medium">View Only</p>
                  <p className="text-sm text-muted-foreground">
                    Can view all activities and data but cannot make changes. Perfect for grandparents and extended family.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Invite Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Invite Family Members</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <p className="text-sm">Click "Invite Member" and enter their email address</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <p className="text-sm">Choose their role (Parent or Caregiver) and permission level</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <p className="text-sm">They'll receive an email invitation to join your family</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                  4
                </div>
                <p className="text-sm">Once they accept, they can access your baby's data based on their permissions</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

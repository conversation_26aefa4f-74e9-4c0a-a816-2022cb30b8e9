"use client"

import { useState } from "react"
import { DashboardLayout } from "@/components/dashboard/layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { 
  Moon, 
  Sun,
  Clock,
  Play,
  Square,
  BarChart3,
  Calendar
} from "lucide-react"
import { formatTime, formatDuration } from "@/lib/utils"

export default function SleepPage() {
  const [isSleeping, setIsSleeping] = useState(false)
  const [sleepStart, setSleepStart] = useState<Date | null>(null)

  const startSleep = () => {
    setIsSleeping(true)
    setSleepStart(new Date())
  }

  const endSleep = () => {
    setIsSleeping(false)
    setSleepStart(null)
  }

  // Mock sleep data
  const todayStats = {
    totalSleep: 8.5,
    naps: 3,
    longestNap: 2.5,
    nightSleep: 6
  }

  const recentSleep = [
    {
      id: 1,
      type: "nap",
      startTime: new Date(Date.now() - 3 * 60 * 60 * 1000),
      endTime: new Date(Date.now() - 1.5 * 60 * 60 * 1000),
      duration: 90
    },
    {
      id: 2,
      type: "night",
      startTime: new Date(Date.now() - 12 * 60 * 60 * 1000),
      endTime: new Date(Date.now() - 6 * 60 * 60 * 1000),
      duration: 360
    },
    {
      id: 3,
      type: "nap",
      startTime: new Date(Date.now() - 18 * 60 * 60 * 1000),
      endTime: new Date(Date.now() - 17 * 60 * 60 * 1000),
      duration: 60
    }
  ]

  return (
    <DashboardLayout
      title="Sleep Tracking"
      description="Monitor your baby's sleep patterns and schedule"
    >
      <div className="space-y-6">
        {/* Today's Sleep Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Moon className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold">{todayStats.totalSleep}h</p>
                  <p className="text-sm text-muted-foreground">Total Sleep</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Sun className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="text-2xl font-bold">{todayStats.naps}</p>
                  <p className="text-sm text-muted-foreground">Naps</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-2xl font-bold">{todayStats.longestNap}h</p>
                  <p className="text-sm text-muted-foreground">Longest Nap</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Moon className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-2xl font-bold">{todayStats.nightSleep}h</p>
                  <p className="text-sm text-muted-foreground">Night Sleep</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sleep Timer */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Moon className="h-5 w-5 mr-2" />
              Sleep Timer
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isSleeping ? (
              <div className="text-center space-y-6">
                <div className="p-8 bg-blue-50 rounded-lg border-2 border-blue-200">
                  <Moon className="h-16 w-16 mx-auto mb-4 text-blue-500" />
                  <p className="text-4xl font-mono font-bold text-blue-600 mb-2">
                    {sleepStart && formatDuration(Math.floor((Date.now() - sleepStart.getTime()) / (1000 * 60)))}
                  </p>
                  <p className="text-blue-600 font-medium">Baby is sleeping</p>
                  <p className="text-sm text-blue-500 mt-2">
                    Started at {sleepStart && formatTime(sleepStart)}
                  </p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <Button variant="outline" size="lg">
                    <Clock className="h-5 w-5 mr-2" />
                    Pause
                  </Button>
                  <Button onClick={endSleep} size="lg">
                    <Square className="h-5 w-5 mr-2" />
                    Wake Up
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center space-y-6">
                <div className="p-8 border-2 border-dashed border-gray-300 rounded-lg">
                  <Moon className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-600 font-medium mb-2">Ready to track sleep</p>
                  <p className="text-sm text-gray-500">
                    Start the timer when baby falls asleep
                  </p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <Button onClick={startSleep} size="lg" className="bg-blue-500 hover:bg-blue-600">
                    <Play className="h-5 w-5 mr-2" />
                    Start Nap
                  </Button>
                  <Button onClick={startSleep} size="lg" className="bg-purple-500 hover:bg-purple-600">
                    <Moon className="h-5 w-5 mr-2" />
                    Start Night Sleep
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Sleep Pattern Chart Placeholder */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Sleep Patterns
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-48 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-600 font-medium">Sleep Pattern Chart</p>
                <p className="text-sm text-gray-500 mt-1">
                  Visual timeline of sleep and wake periods
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Sleep Sessions */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Recent Sleep Sessions
              </CardTitle>
              <Button variant="outline" size="sm">
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentSleep.map((session) => (
                <div
                  key={session.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      session.type === 'night' ? 'bg-purple-100' : 'bg-blue-100'
                    }`}>
                      {session.type === 'night' ? (
                        <Moon className="h-5 w-5 text-purple-600" />
                      ) : (
                        <Sun className="h-5 w-5 text-blue-600" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">
                        {session.type === 'night' ? 'Night Sleep' : 'Nap'}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {formatTime(session.startTime)} - {formatTime(session.endTime)} • {formatDuration(session.duration)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Button variant="ghost" size="sm">
                      Edit
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

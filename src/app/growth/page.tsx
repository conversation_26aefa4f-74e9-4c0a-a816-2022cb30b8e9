"use client"

import { DashboardLayout } from "@/components/dashboard/layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { 
  Scale, 
  Ruler, 
  TrendingUp,
  Plus,
  Calendar
} from "lucide-react"
import { formatDate } from "@/lib/utils"

// Mock growth data
const growthData = [
  {
    id: 1,
    date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    weight: 12.2,
    height: 23.5,
    headCircumference: 15.8
  },
  {
    id: 2,
    date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
    weight: 11.8,
    height: 23.2,
    headCircumference: 15.6
  },
  {
    id: 3,
    date: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
    weight: 11.5,
    height: 22.9,
    headCircumference: 15.4
  }
]

const currentStats = {
  weight: 12.2,
  height: 23.5,
  headCircumference: 15.8,
  weightPercentile: 65,
  heightPercentile: 72,
  headPercentile: 58
}

export default function GrowthPage() {
  return (
    <DashboardLayout
      title="Growth Tracking"
      description="Monitor your baby's growth and development"
    >
      <div className="space-y-6">
        {/* Current Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 lg:gap-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Scale className="h-5 w-5 mr-2 text-blue-500" />
                Weight
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-3xl font-bold">{currentStats.weight} lbs</p>
                  <p className="text-sm text-green-600 font-medium">+0.4 lbs this week</p>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Percentile</span>
                    <span>{currentStats.weightPercentile}%</span>
                  </div>
                  <Progress value={currentStats.weightPercentile} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Ruler className="h-5 w-5 mr-2 text-green-500" />
                Height
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-3xl font-bold">{currentStats.height}"</p>
                  <p className="text-sm text-green-600 font-medium">+0.3" this week</p>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Percentile</span>
                    <span>{currentStats.heightPercentile}%</span>
                  </div>
                  <Progress value={currentStats.heightPercentile} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <div className="h-5 w-5 mr-2 bg-purple-500 rounded-full"></div>
                Head
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-3xl font-bold">{currentStats.headCircumference}"</p>
                  <p className="text-sm text-green-600 font-medium">+0.2" this week</p>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Percentile</span>
                    <span>{currentStats.headPercentile}%</span>
                  </div>
                  <Progress value={currentStats.headPercentile} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Add New Measurement */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Plus className="h-5 w-5 mr-2" />
              Add New Measurement
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium">Weight (lbs)</label>
                <Input type="number" step="0.1" placeholder="12.2" className="mt-1" />
              </div>
              <div>
                <label className="text-sm font-medium">Height (inches)</label>
                <Input type="number" step="0.1" placeholder="23.5" className="mt-1" />
              </div>
              <div>
                <label className="text-sm font-medium">Head Circumference (inches)</label>
                <Input type="number" step="0.1" placeholder="15.8" className="mt-1" />
              </div>
              <div className="flex items-end">
                <Button className="w-full">
                  Record Measurement
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Growth Chart Placeholder */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Growth Chart
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
              <div className="text-center">
                <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-600 font-medium">Interactive Growth Chart</p>
                <p className="text-sm text-gray-500 mt-1">
                  Visual representation of growth trends over time
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Measurements */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Recent Measurements
              </CardTitle>
              <Button variant="outline" size="sm">
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {growthData.map((measurement) => (
                <div
                  key={measurement.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                      <Scale className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">{formatDate(measurement.date)}</p>
                      <p className="text-sm text-muted-foreground">
                        Weight: {measurement.weight} lbs • Height: {measurement.height}" • Head: {measurement.headCircumference}"
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Button variant="ghost" size="sm">
                      Edit
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

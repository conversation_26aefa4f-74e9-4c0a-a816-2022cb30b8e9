"use client"

import { DashboardLayout } from "@/components/dashboard/layout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Baby, 
  Droplets,
  Circle,
  Calendar,
  Clock,
  BarChart3
} from "lucide-react"
import { formatTime, getTimeSince } from "@/lib/utils"

export default function DiapersPage() {
  // Mock diaper data
  const todayStats = {
    total: 6,
    wet: 4,
    dirty: 2,
    lastChange: new Date(Date.now() - 45 * 60 * 1000)
  }

  const recentChanges = [
    {
      id: 1,
      time: new Date(Date.now() - 45 * 60 * 1000),
      type: "wet",
      notes: ""
    },
    {
      id: 2,
      time: new Date(Date.now() - 2 * 60 * 60 * 1000),
      type: "dirty",
      notes: "Normal consistency"
    },
    {
      id: 3,
      time: new Date(Date.now() - 3.5 * 60 * 60 * 1000),
      type: "wet",
      notes: ""
    },
    {
      id: 4,
      time: new Date(Date.now() - 5 * 60 * 60 * 1000),
      type: "both",
      notes: ""
    }
  ]

  const getDiaperIcon = (type: string) => {
    switch (type) {
      case "wet":
        return <Droplets className="h-5 w-5 text-blue-500" />
      case "dirty":
        return <Circle className="h-5 w-5 text-orange-500" />
      case "both":
        return <Baby className="h-5 w-5 text-purple-500" />
      default:
        return <Baby className="h-5 w-5 text-gray-500" />
    }
  }

  const getDiaperColor = (type: string) => {
    switch (type) {
      case "wet":
        return "bg-blue-100 border-blue-200"
      case "dirty":
        return "bg-orange-100 border-orange-200"
      case "both":
        return "bg-purple-100 border-purple-200"
      default:
        return "bg-gray-100 border-gray-200"
    }
  }

  return (
    <DashboardLayout
      title="Diaper Changes"
      description="Track diaper changes and patterns"
    >
      <div className="space-y-6">
        {/* Today's Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Baby className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-2xl font-bold">{todayStats.total}</p>
                  <p className="text-sm text-muted-foreground">Total Changes</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Droplets className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold">{todayStats.wet}</p>
                  <p className="text-sm text-muted-foreground">Wet Diapers</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Circle className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="text-2xl font-bold">{todayStats.dirty}</p>
                  <p className="text-sm text-muted-foreground">Dirty Diapers</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-2xl font-bold">{getTimeSince(todayStats.lastChange)}</p>
                  <p className="text-sm text-muted-foreground">Last Change</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Add Diaper Change */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Baby className="h-5 w-5 mr-2" />
              Record Diaper Change
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <Button 
                  size="lg" 
                  className="h-20 flex-col space-y-2 bg-blue-500 hover:bg-blue-600"
                >
                  <Droplets className="h-6 w-6" />
                  <span>Wet Only</span>
                </Button>
                
                <Button 
                  size="lg" 
                  className="h-20 flex-col space-y-2 bg-orange-500 hover:bg-orange-600"
                >
                  <Circle className="h-6 w-6" />
                  <span>Dirty Only</span>
                </Button>
                
                <Button 
                  size="lg" 
                  className="h-20 flex-col space-y-2 bg-purple-500 hover:bg-purple-600"
                >
                  <Baby className="h-6 w-6" />
                  <span>Both</span>
                </Button>
              </div>
              
              <div className="border-t pt-4">
                <h4 className="font-medium mb-3">Additional Notes (Optional)</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Consistency</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="">Select...</option>
                      <option value="normal">Normal</option>
                      <option value="soft">Soft</option>
                      <option value="hard">Hard</option>
                      <option value="watery">Watery</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Color</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="">Select...</option>
                      <option value="yellow">Yellow</option>
                      <option value="brown">Brown</option>
                      <option value="green">Green</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pattern Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Weekly Pattern
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-48 bg-gradient-to-br from-green-50 to-blue-50 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-600 font-medium">Diaper Change Patterns</p>
                <p className="text-sm text-gray-500 mt-1">
                  Track frequency and timing patterns over the week
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Changes */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Recent Changes
              </CardTitle>
              <Button variant="outline" size="sm">
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentChanges.map((change) => (
                <div
                  key={change.id}
                  className={`flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors ${getDiaperColor(change.type)}`}
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center border">
                      {getDiaperIcon(change.type)}
                    </div>
                    <div>
                      <p className="font-medium capitalize">
                        {change.type === "both" ? "Wet & Dirty" : change.type} Diaper
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {change.notes || "No additional notes"}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">{formatTime(change.time)}</p>
                    <p className="text-xs text-muted-foreground">
                      {getTimeSince(change.time)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

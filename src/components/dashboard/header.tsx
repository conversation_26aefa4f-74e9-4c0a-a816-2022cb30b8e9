"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Bell, Plus, Search } from "lucide-react"
import { Input } from "@/components/ui/input"

interface HeaderProps {
  title: string
  description?: string
  action?: React.ReactNode
}

export function Header({ title, description, action }: HeaderProps) {
  return (
    <header className="w-full bg-white border-b border-gray-200 shadow-sm">
      <div className="flex h-16 items-center justify-between px-4 lg:px-6">
        {/* Mobile spacing for hamburger menu */}
        <div className="w-12 lg:hidden"></div>

        {/* Title section */}
        <div className="flex-1 min-w-0">
          <h1 className="text-xl font-bold text-gray-900 lg:text-2xl">
            {title}
          </h1>
          {description && (
            <p className="text-sm text-gray-600 hidden sm:block">
              {description}
            </p>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-3">
          {/* Search - hidden on mobile */}
          <div className="hidden md:flex items-center">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search..."
                className="pl-10 w-64"
              />
            </div>
          </div>

          {/* Notifications */}
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
          </Button>

          {/* Custom action */}
          {action}
        </div>
      </div>
    </header>
  )
}

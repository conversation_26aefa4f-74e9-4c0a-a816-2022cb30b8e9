"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Bell, Plus, Search } from "lucide-react"
import { Input } from "@/components/ui/input"

interface HeaderProps {
  title: string
  description?: string
  action?: React.ReactNode
}

export function Header({ title, description, action }: HeaderProps) {
  return (
    <div className="border-b bg-white dark:bg-gray-900 shadow-sm sticky top-0 z-40">
      <div className="flex h-16 items-center px-4 lg:px-6">
        {/* Mobile spacing for hamburger menu */}
        <div className="w-12 lg:hidden"></div>

        {/* Title section */}
        <div className="flex-1 min-w-0">
          <h1 className="text-xl font-semibold truncate lg:text-2xl text-gray-900 dark:text-white">
            {title}
          </h1>
          {description && (
            <p className="text-sm text-gray-600 dark:text-gray-300 hidden sm:block">
              {description}
            </p>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 lg:space-x-4">
          {/* Search - hidden on mobile */}
          <div className="hidden md:flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search..."
                className="pl-8 w-64"
              />
            </div>
          </div>

          {/* Notifications */}
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs"></span>
          </Button>

          {/* Custom action */}
          {action}
        </div>
      </div>
    </div>
  )
}

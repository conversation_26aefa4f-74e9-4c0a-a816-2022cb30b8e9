"use client"

import { Sidebar } from "./sidebar"
import { <PERSON><PERSON> } from "./header"
import { cn } from "@/lib/utils"

interface DashboardLayoutProps {
  children: React.ReactNode
  title: string
  description?: string
  headerAction?: React.ReactNode
  className?: string
}

export function DashboardLayout({
  children,
  title,
  description,
  headerAction,
  className
}: DashboardLayoutProps) {
  console.log("DashboardLayout rendering with title:", title) // Debug log

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar />

      {/* Main content */}
      <div className="lg:pl-64">
        <Header
          title={title || "Dashboard"}
          description={description}
          action={headerAction}
        />

        <main className={cn("flex-1 bg-white", className)}>
          <div className="container mx-auto px-4 py-6 lg:px-6 lg:py-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

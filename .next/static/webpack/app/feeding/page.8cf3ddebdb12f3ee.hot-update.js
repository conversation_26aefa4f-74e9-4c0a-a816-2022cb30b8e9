"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feeding/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/sidebar.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,BarChart3,Calendar,Heart,Home,Menu,Moon,Settings,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,BarChart3,Calendar,Heart,Home,Menu,Moon,Settings,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,BarChart3,Calendar,Heart,Home,Menu,Moon,Settings,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,BarChart3,Calendar,Heart,Home,Menu,Moon,Settings,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,BarChart3,Calendar,Heart,Home,Menu,Moon,Settings,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/baby.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,BarChart3,Calendar,Heart,Home,Menu,Moon,Settings,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,BarChart3,Calendar,Heart,Home,Menu,Moon,Settings,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,BarChart3,Calendar,Heart,Home,Menu,Moon,Settings,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,BarChart3,Calendar,Heart,Home,Menu,Moon,Settings,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Baby,BarChart3,Calendar,Heart,Home,Menu,Moon,Settings,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Today\",\n        href: \"/today\",\n        icon: _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Feeding\",\n        href: \"/feeding\",\n        icon: _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Sleep\",\n        href: \"/sleep\",\n        icon: _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Diapers\",\n        href: \"/diapers\",\n        icon: _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: \"Growth\",\n        href: \"/growth\",\n        icon: _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Family\",\n        href: \"/family\",\n        icon: _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    }\n];\nfunction Sidebar(param) {\n    let { className } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed top-4 left-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"outline\",\n                    size: \"icon\",\n                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                    className: \"bg-background shadow-md\",\n                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 z-40 bg-black/50\",\n                onClick: ()=>setIsMobileMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"fixed inset-y-0 left-0 z-50 w-64 bg-background border-r transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0\", isMobileMenuOpen ? \"translate-x-0\" : \"-translate-x-full\", className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-16 px-4 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Baby_BarChart3_Calendar_Heart_Home_Menu_Moon_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold\",\n                                        children: \"BabyTracker\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 py-6 space-y-2\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: item.href,\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors\", isActive ? \"bg-primary text-primary-foreground\" : \"text-muted-foreground hover:text-foreground hover:bg-accent\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-primary-foreground\",\n                                            children: \"U\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium truncate\",\n                                                children: \"User\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground truncate\",\n                                                children: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/sidebar.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"g4np8q1LY+g6GKLSpS3pKVVL1eg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/sidebar.tsx\n"));

/***/ })

});
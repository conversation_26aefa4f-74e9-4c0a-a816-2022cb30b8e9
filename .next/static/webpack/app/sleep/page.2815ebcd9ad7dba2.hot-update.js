"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/sleep/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/header.tsx":
/*!*********************************************!*\
  !*** ./src/components/dashboard/header.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bell!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\nfunction Header(param) {\n    let { title, description, action } = param;\n    console.log(\"Header rendering with title:\", title) // Debug log\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"border-b-2 border-red-500 bg-blue-100 shadow-lg sticky top-0 z-40 min-h-[64px]\",\n        style: {\n            backgroundColor: \"#f0f0f0\",\n            border: \"2px solid red\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-16 items-center px-4 lg:px-6 max-w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-12 lg:hidden flex-shrink-0 bg-yellow-200\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/header.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0 mr-4 bg-green-200 p-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-black lg:text-2xl truncate\",\n                            style: {\n                                color: \"red\",\n                                fontSize: \"24px\"\n                            },\n                            children: title || \"TEST DASHBOARD\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 hidden sm:block truncate\",\n                            style: {\n                                color: \"blue\"\n                            },\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/header.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/header.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 lg:space-x-4 flex-shrink-0 bg-purple-200 p-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            className: \"relative bg-orange-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/header.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/header.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/header.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        action\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/header.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/header.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Baby tracker/src/components/dashboard/header.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/header.tsx\n"));

/***/ })

});